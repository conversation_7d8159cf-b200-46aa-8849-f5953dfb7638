using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

[RequireComponent(typeof(MeshRenderer))]
public class MaterialPropertyOverride : MonoBehaviour
{
    public int m_subMeshIndex;
    public List<PropertyOverride> m_materialPropertyOverrides;
    
    [Serializable]
    public class PropertyOverride
    {
        public string m_name;
        public float m_overrideF;
        public Color m_overrideC;
        public Vector4 m_overrideV;
        public int m_overrideI;

        public static bool IsEq(Vector4 _a, Vector4 _b)
        {
            return IsEq(_a.x, _b.x) && IsEq(_a.y, _b.y) && IsEq(_a.z, _b.z) && IsEq(_a.w, _b.w);
        }
        
        public static bool IsEq(float _a, float _b)
        {
            const float epsilon = 0.00001f;
            if (_a.Equals(_b))
                return true;
            bool aDenormal = float.IsNaN(_a) || float.IsInfinity(_a);
            bool bDenormal = float.IsNaN(_b) || float.IsInfinity(_b);
            if (aDenormal || bDenormal)
                return aDenormal && bDenormal;
            float divisor = Math.Max(_a, _b);
            if (divisor <= 0)
                divisor = Math.Min(_a, _b);
            return Math.Abs(_a - _b) / divisor <= epsilon;
        }
    }

    private static readonly string[] s_blackListedProperties =
    {
        "_R", "_G", "_B", "_A", "_R_ON_OFF", "_G_Blend", "_B_Blend", "_A_Blend", "_QueueControl"
    };
    
    public static void TryAdd(Material _old, Material _new, int _subMesh, GameObject _addTo)
    {
        var properties = new List<PropertyOverride>();
        var shader = _old.shader;
        var propCount = shader.GetPropertyCount();
        for (int i = 0; i < propCount; ++i)
        {
            var name = shader.GetPropertyName(i);
            if (s_blackListedProperties.Contains(name))
                continue;
            
            var type = shader.GetPropertyType(i);
            var id = shader.GetPropertyNameId(i);
            switch (type)
            {
                case ShaderPropertyType.Color:
                    var color = _old.GetColor(id);
                    if (PropertyOverride.IsEq(_new.GetColor(id), color))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideC = color });
                    break;
                case ShaderPropertyType.Vector:
                    var vector = _old.GetVector(id);
                    if (PropertyOverride.IsEq(_new.GetVector(id), vector))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideV = vector });
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    var f = _old.GetFloat(id);
                    if (PropertyOverride.IsEq(_new.GetFloat(id), f))
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideF = f });
                    break;
                case ShaderPropertyType.Int:
                    var n = _old.GetInt(id);
                    if (_new.GetInt(id) == n)
                        break;
                    properties.Add(new PropertyOverride { m_name = name, m_overrideI = n });
                    break;
            }
        }
        if (properties.Count == 0)
            return;

        var mpo = _addTo.AddComponent<MaterialPropertyOverride>();
        mpo.m_subMeshIndex = _subMesh;
        mpo.m_materialPropertyOverrides = properties;
    }

    private void OnEnable()
    {
        try
        {
            var mr = GetComponent<MeshRenderer>();
            var mats = mr.materials;
            var mat = mats[m_subMeshIndex];
            foreach (var mp in m_materialPropertyOverrides)
            {
                int propertyIndex = mat.shader.FindPropertyIndex(mp.m_name);
                var type = mat.shader.GetPropertyType(propertyIndex);
                var id = mat.shader.GetPropertyNameId(propertyIndex);
                switch (type)
                {
                    case ShaderPropertyType.Color:
                        mat.SetColor(id, mp.m_overrideC);
                        break;
                    case ShaderPropertyType.Vector:
                        mat.SetVector(id, mp.m_overrideV);
                        break;
                    case ShaderPropertyType.Float:
                    case ShaderPropertyType.Range:
                        mat.SetFloat(id, mp.m_overrideF);
                        break;
                    case ShaderPropertyType.Int:
                        mat.SetInt(id, mp.m_overrideI);
                        break;
                    default:
                        Debug.LogError($"Unsupported property type {type} for '{mp.m_name}'");
                        break;
                }
            }
            mats[m_subMeshIndex] = mat;
            mr.materials = mats;
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }

#if UNITY_EDITOR
    public Material testAgainst;
    public bool test;
    
    private void Update()
    {
        if (test)
            TestAgainst(testAgainst);
        test = false;
    }

    private void TestAgainst(Material _other)
    {
        var mat = GetComponent<MeshRenderer>().sharedMaterials[m_subMeshIndex];
        var shader = mat.shader;
        var propCount = shader.GetPropertyCount();
        for (int i = 0; i < propCount; ++i)
        {
            var name = shader.GetPropertyName(i);
            var type = shader.GetPropertyType(i);
            var id = shader.GetPropertyNameId(i);
            switch (type)
            {
                case ShaderPropertyType.Color:
                    var color = mat.GetColor(id);
                    var otherColor = _other.GetColor(id);
                    if (otherColor == color)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {color}, other value is {otherColor}");
                    break;
                case ShaderPropertyType.Vector:
                    var vector = mat.GetVector(id);
                    var otherVector = _other.GetVector(id);
                    if (otherVector == vector)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {vector}, other value is {otherVector}");
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    var f = mat.GetFloat(id);
                    var otherF = _other.GetFloat(id);
                    if ((otherF - f).IsZero())
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {f}, other value is {otherF}");
                    break;
                case ShaderPropertyType.Texture:
                    var hash = mat.GetTexture(id).imageContentsHash;
                    var otherHash = _other.GetTexture(id).imageContentsHash;
                    if (hash == otherHash)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {hash}, other value is {otherHash}");
                    break;
                case ShaderPropertyType.Int:
                    var n = mat.GetInt(id);
                    var otherN = _other.GetInt(id);
                    if (otherN == n)
                        break;
                    Debug.LogError($"Discrepancy found: Property {name}, my value is {n}, other value is {otherN}");
                    break;
            }
        }
    }
#endif
}
