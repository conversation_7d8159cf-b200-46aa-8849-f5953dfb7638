using System;
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

[CustomEditor(typeof(MaterialPropertyOverride))]
public class MaterialPropertyOverrideEditor : Editor
{
    private MaterialPropertyOverride m_target;
    private MaterialPropertyBlock m_previewPropertyBlock;
    private bool m_previewEnabled = false;
    private bool m_wasPreviewEnabled = false;
    
    // Static to persist across selections
    private static bool s_globalPreviewEnabled = false;
    
    private void OnEnable()
    {
        m_target = (MaterialPropertyOverride)target;
        m_previewPropertyBlock = new MaterialPropertyBlock();
        m_previewEnabled = s_globalPreviewEnabled;
        
        // Subscribe to editor update for continuous preview
        EditorApplication.update += OnEditorUpdate;
        
        // Apply preview if enabled
        if (m_previewEnabled)
        {
            ApplyPreview();
        }
    }
    
    private void OnDisable()
    {
        // Unsubscribe from editor update
        EditorApplication.update -= OnEditorUpdate;
        
        // Clear preview when disabling
        ClearPreview();
    }
    
    private void OnEditorUpdate()
    {
        // Only update if preview state changed or preview is enabled
        if (m_previewEnabled != m_wasPreviewEnabled)
        {
            if (m_previewEnabled)
            {
                ApplyPreview();
            }
            else
            {
                ClearPreview();
            }
            m_wasPreviewEnabled = m_previewEnabled;
        }
        else if (m_previewEnabled)
        {
            // Continuously apply preview to handle material changes
            ApplyPreview();
        }
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        // Preview toggle section
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("Editor Preview", EditorStyles.boldLabel);
        
        EditorGUI.BeginChangeCheck();
        m_previewEnabled = EditorGUILayout.Toggle("Enable Preview", m_previewEnabled);
        if (EditorGUI.EndChangeCheck())
        {
            s_globalPreviewEnabled = m_previewEnabled;
        }
        
        if (m_previewEnabled)
        {
            EditorGUILayout.HelpBox("Preview is active. Material property overrides are visible using MaterialPropertyBlocks. This does not affect the actual prefab or materials.", MessageType.Info);
        }
        else
        {
            EditorGUILayout.HelpBox("Preview is disabled. Enable to see material property overrides in the editor.", MessageType.None);
        }
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
        
        // Draw default inspector
        DrawDefaultInspector();
        
        // Show override information
        if (m_target.m_materialPropertyOverrides != null && m_target.m_materialPropertyOverrides.Count > 0)
        {
            EditorGUILayout.Space();
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField($"Property Overrides ({m_target.m_materialPropertyOverrides.Count})", EditorStyles.boldLabel);
            
            foreach (var prop in m_target.m_materialPropertyOverrides)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(prop.m_name, GUILayout.Width(150));
                
                // Show the override value based on type
                var renderer = m_target.GetComponent<MeshRenderer>();
                if (renderer != null && renderer.sharedMaterials.Length > m_target.m_subMeshIndex)
                {
                    var material = renderer.sharedMaterials[m_target.m_subMeshIndex];
                    if (material != null && material.shader != null)
                    {
                        int propIndex = material.shader.FindPropertyIndex(prop.m_name);
                        if (propIndex >= 0)
                        {
                            var propType = material.shader.GetPropertyType(propIndex);
                            switch (propType)
                            {
                                case ShaderPropertyType.Color:
                                    EditorGUILayout.ColorField(prop.m_overrideC);
                                    break;
                                case ShaderPropertyType.Vector:
                                    EditorGUILayout.Vector4Field("", prop.m_overrideV);
                                    break;
                                case ShaderPropertyType.Float:
                                case ShaderPropertyType.Range:
                                    EditorGUILayout.FloatField(prop.m_overrideF);
                                    break;
                                case ShaderPropertyType.Int:
                                    EditorGUILayout.IntField(prop.m_overrideI);
                                    break;
                            }
                        }
                    }
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }
        
        serializedObject.ApplyModifiedProperties();
    }
    
    private void ApplyPreview()
    {
        if (m_target == null || m_target.m_materialPropertyOverrides == null)
            return;
            
        var renderer = m_target.GetComponent<MeshRenderer>();
        if (renderer == null)
            return;
            
        // Clear the property block first
        m_previewPropertyBlock.Clear();
        
        // Get the material for the specified submesh
        if (renderer.sharedMaterials.Length <= m_target.m_subMeshIndex)
            return;
            
        var material = renderer.sharedMaterials[m_target.m_subMeshIndex];
        if (material == null || material.shader == null)
            return;
        
        // Apply each property override to the MaterialPropertyBlock
        foreach (var prop in m_target.m_materialPropertyOverrides)
        {
            int propIndex = material.shader.FindPropertyIndex(prop.m_name);
            if (propIndex < 0)
                continue;
                
            var propType = material.shader.GetPropertyType(propIndex);
            var propId = material.shader.GetPropertyNameId(propIndex);
            
            switch (propType)
            {
                case ShaderPropertyType.Color:
                    m_previewPropertyBlock.SetColor(propId, prop.m_overrideC);
                    break;
                case ShaderPropertyType.Vector:
                    m_previewPropertyBlock.SetVector(propId, prop.m_overrideV);
                    break;
                case ShaderPropertyType.Float:
                case ShaderPropertyType.Range:
                    m_previewPropertyBlock.SetFloat(propId, prop.m_overrideF);
                    break;
                case ShaderPropertyType.Int:
                    m_previewPropertyBlock.SetInt(propId, prop.m_overrideI);
                    break;
            }
        }
        
        // Apply the MaterialPropertyBlock to the renderer
        renderer.SetPropertyBlock(m_previewPropertyBlock, m_target.m_subMeshIndex);
        
        // Force scene view to repaint
        SceneView.RepaintAll();
    }
    
    private void ClearPreview()
    {
        if (m_target == null)
            return;
            
        var renderer = m_target.GetComponent<MeshRenderer>();
        if (renderer == null)
            return;
        
        // Clear the MaterialPropertyBlock to restore original material appearance
        renderer.SetPropertyBlock(null, m_target.m_subMeshIndex);
        
        // Force scene view to repaint
        SceneView.RepaintAll();
    }
}
